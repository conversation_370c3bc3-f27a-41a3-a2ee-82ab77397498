import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import StaffPage from './StaffPage';
import ScreenPage from './ScreenPage';

const mockOrders = Array.from({ length: 20 }, (_, i) => ({
  id: i + 1,
  orderNumber: `Order-${i + 1}`,
  status: 'Queue',
}));

export default function App() {
  const [orders, setOrders] = React.useState(mockOrders);

  return (
    <BrowserRouter>
      <Routes>
        <Route path="/staff" element={<StaffPage orders={orders} setOrders={setOrders} />} />
        <Route path="/screen" element={<ScreenPage orders={orders} />} />
      </Routes>
    </BrowserRouter>
  );
}