import React from 'react';

export default function StaffPage({ orders, setOrders }) {
  const today = new Date().toLocaleDateString();

  const updateOrderStatus = (id) => {
    setOrders((prevOrders) =>
      prevOrders.map((order) =>
        order.id === id
          ? {
              ...order,
              status:
                order.status === 'Queue'
                  ? 'In Progress'
                  : order.status === 'In Progress'
                  ? 'Ready'
                  : 'Ready',
            }
          : order
      )
    );
  };

  return (
    <div>
      <h1>Staff Page</h1>
      <p>Today's Date: {today}</p>
      <ul>
        {orders.map((order) => (
          <li key={order.id}>
            {order.orderNumber} - {order.status}{' '}
            {order.status !== 'Ready' && (
              <button onClick={() => updateOrderStatus(order.id)}>Next Status</button>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
}